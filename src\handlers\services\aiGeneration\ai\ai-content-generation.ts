import { v4 as uuid4 } from 'uuid';

import {
  createMessagesAPIConfig,
  createMessagesAPITextMessage,
  createThinkingConfig,
  invokeClaudeWithThinkingStream,
  invokeModelByBedrock,
  MessagesAPIMessage,
  sendClaudeMessageToClient,
  StreamCallback,
  StreamEvent,
} from '@shared/utils/bedrock';
import { BEDROCK_CLAUDE_MODEL_ID } from '@shared/utils/const';
import { aiChat } from '@shared/utils/llm';
import { IPersona } from '@src/core/types/interface';
import { getSettingPrompt } from '@src/infrastructure/database/db/setting';

// 候选内容类型
interface CandidateContent {
  title: string;
  content: string;
  style: string;
}

// 选中照片类型
interface SelectedPhoto {
  id: string;
  summary: string;
}

// 流式输出结束回调类型
interface StreamEndCallback {
  (result: {
    thinkingContent: string;
    finalContent: string;
    taskId: string;
    connectionId: string;
  }): void | Promise<void>;
}

/**
 * 生成最终内容的提示词模板
 */
const createFinalContentPrompt = async (
  candidateContent: CandidateContent[],
  persona: IPersona,
  selectedPhotos: SelectedPhoto[],
): Promise<string> => {
  const candidateList = candidateContent
    .map(
      (candidate, index) => `候选内容${index + 1}：
标题：${candidate.title}
内容：${candidate.content}
风格：${candidate.style}`,
    )
    .join('\n\n');

  const photoDescriptions = selectedPhotos
    .map((photo, index) => `照片${index + 1} (ID: ${photo.id}): ${photo.summary}`)
    .join('\n');
  const { generate } = await getSettingPrompt();
  const promptTemplate = generate;

  return promptTemplate
    .replace('{{PERSONA_NAME}}', persona.name)
    .replace('{{PERSONA_GENDER}}', persona.gender)
    .replace('{{PERSONA_MBTI}}', persona.mbti)
    .replace('{{PERSONA_PERSONALITY}}', persona.personality)
    .replace('{{PERSONA_INTRODUCTION}}', persona.introduction)
    .replace('{{PERSONA_TOPICS}}', persona.topics)
    .replace('{{CANDIDATE_CONTENT}}', candidateList || '暂无候选内容')
    .replace('{{PHOTO_DESCRIPTIONS}}', photoDescriptions || '暂无相关照片');
};

export const parseFinalContentResponse = async (response: string) => {
  try {
    // 尝试解析JSON格式的响应
    const parsed = JSON.parse(response);
    const result = parsed.map((item: any) => ({
      ...item,
      id: uuid4(),
    }));
    console.log('result=====>', result);
    return result;
  } catch (error) {
    const parsedJson: any = await aiChat([
      {
        role: 'system',
        content: `你将收到一段错误的JSON字符串: ${response}
          请按照如下要求修复，要求：
          1. 不要添加 markdown 标记，不要加任何注释或额外说明。
          2. 所有字段必须使用双引号，结构必须完整，值不可为 undefined、NaN、函数等非法内容。
          3. 输出内容必须是合法 JSON 格式对象。最终返回的 JSON 应该能直接通过 JSON.parse 成功解析。
          返回格式如下：
          {
            "json": "修复后的内容"
          }`,
      },
    ]);
    console.log('response11223323', parsedJson);
    return JSON.parse(parsedJson.json);
  }
};

/**
 * 创建增强的流处理器，支持内容收集和结束回调
 * @param connectionId WebSocket连接ID
 * @param taskId 任务ID
 * @param options 配置选项
 * @param onStreamEnd 流式输出结束回调
 * @returns 流式传输回调函数
 */
const createEnhancedStreamHandler = (
  connectionId: string,
  taskId: string,
  options: {
    sendThinking?: boolean;
    sendProgress?: boolean;
    bufferSize?: number;
  } = {},
  onStreamEnd?: StreamEndCallback,
): StreamCallback => {
  const { sendThinking = true, sendProgress = true, bufferSize = 100 } = options;

  let thinkingBuffer = '';
  let textBuffer = '';
  let currentIndex = -1;

  // 收集完整的思考过程和正式内容
  let fullThinkingContent = '';
  let fullFinalContent = '';

  return async (event: StreamEvent) => {
    try {
      switch (event.type) {
        case 'message_start':
          if (sendProgress) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'start',
              message: 'Claude开始思考...',
            });
          }
          break;

        case 'content_block_start':
          currentIndex = event.index;
          if (event.content_block.type === 'thinking' && sendThinking) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'thinking',
              content: '',
              index: currentIndex,
            });
          } else if (event.content_block.type === 'text') {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'text',
              content: '',
              index: currentIndex,
            });
          }
          break;

        case 'content_block_delta':
          if (event.delta.type === 'thinking_delta' && sendThinking) {
            const thinkingDelta = event.delta.thinking || '';
            thinkingBuffer += thinkingDelta;
            fullThinkingContent += thinkingDelta; // 收集完整思考内容

            // 按缓冲区大小发送思考内容
            if (thinkingBuffer.length >= bufferSize) {
              await sendClaudeMessageToClient(connectionId, {
                taskId,
                type: 'thinking',
                content: thinkingBuffer,
                index: event.index,
              });
              thinkingBuffer = '';
            }
          } else if (event.delta.type === 'text_delta') {
            const textDelta = event.delta.text || '';
            textBuffer += textDelta;
            fullFinalContent += textDelta; // 收集完整正式内容

            // 按缓冲区大小发送文本内容
            if (textBuffer.length >= bufferSize) {
              await sendClaudeMessageToClient(connectionId, {
                taskId,
                type: 'text',
                content: textBuffer,
                index: event.index,
              });
              textBuffer = '';
            }
          }
          break;

        case 'content_block_stop':
          // 发送剩余的缓冲内容
          if (thinkingBuffer && sendThinking) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'thinking',
              content: thinkingBuffer,
              index: event.index,
            });
            thinkingBuffer = '';
          }

          if (textBuffer) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'text',
              content: textBuffer,
              index: event.index,
            });
            textBuffer = '';
          }
          break;

        case 'message_stop':
          await sendClaudeMessageToClient(connectionId, {
            taskId,
            type: 'complete',
          });

          // 调用流式输出结束回调
          if (onStreamEnd) {
            try {
              await onStreamEnd({
                thinkingContent: fullThinkingContent,
                finalContent: fullFinalContent,
                taskId,
                connectionId,
              });
            } catch (callbackError) {
              console.error('Stream end callback error:', callbackError);
            }
          }
          break;

        default:
          // 处理其他事件类型
          break;
      }
    } catch (error) {
      console.error('Enhanced WebSocket handler error:', error);
      await sendClaudeMessageToClient(connectionId, {
        taskId,
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      if (onStreamEnd) {
        try {
          await onStreamEnd({
            thinkingContent: fullThinkingContent,
            finalContent: fullFinalContent,
            taskId,
            connectionId,
          });
        } catch (callbackError) {
          console.error('Stream end callback error:', callbackError);
        }
      }
    }
  };
};

/**
 * 使用Bedrock生成最终内容
 */
export const generateContent = async (
  candidateContent: CandidateContent[],
  persona: IPersona,
  selectedPhotos: SelectedPhoto[],
) => {
  try {
    if (!persona) {
      throw new Error('人设信息不能为空');
    }
    // 生成提示词
    const prompt = await createFinalContentPrompt(candidateContent, persona, selectedPhotos || []);
    // 调用Bedrock生成最终内容
    const modelId = BEDROCK_CLAUDE_MODEL_ID;
    const response = await invokeModelByBedrock(modelId, {
      anthropic_version: 'bedrock-2023-05-31',
      max_tokens: 10000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });
    if (!response || response.trim().length === 0) {
      throw new Error('Bedrock返回了空响应');
    }
    console.log('response=====>', response);
    const aiResult = await parseFinalContentResponse(response);
    console.log('Generated final content successfully:', {
      contentCount: aiResult.length,
    });
    return aiResult;
  } catch (error) {
    console.error('生成最终内容失败:', error);

    // 返回备用内容
    const fallbackContent = `作为${persona.name}，我想和大家分享一些个人感悟。

${persona.personality}的我，总是能从生活中发现不一样的美好。${persona.introduction}

今天想聊聊${persona.topics.split(',')[0]?.trim() || '生活'}这个话题。每个人都有自己独特的视角和体验，而我希望通过我的分享，能够给大家带来一些启发和思考。

生活中的每一个瞬间都值得被记录和珍惜，让我们一起在平凡中发现不平凡，在日常中创造美好。`;

    return [
      {
        finalContent: fallbackContent,
        title: '生活感悟分享',
        tags: ['生活', '分享', '感悟'],
        style: '个人风格',
        reasoning: `由于AI服务异常，使用了基于人设的备用内容: ${error instanceof Error ? error.message : '未知错误'}`,
        photos: selectedPhotos.map((photo) => photo.id),
        createdAt: new Date().toISOString(),
        status: 'INIT',
        id: uuid4(),
      },
    ];
  }
};

/**
 * 使用Claude Sonnet 4思考过程生成内容（WebSocket流式传输）
 * @param connectionId WebSocket连接ID
 * @param candidateContent 候选内容
 * @param persona 人设信息
 * @param selectedPhotos 选中的照片
 * @param options 配置选项
 * @param onStreamEnd 流式输出结束回调，包含思考过程和正式内容
 */
export const generateContentWithThinking = async (
  connectionId: string,
  taskId: string,
  candidateContent: CandidateContent[],
  persona: IPersona,
  selectedPhotos: SelectedPhoto[],
  options: {
    thinkingBudget?: number;
    temperature?: number;
    maxTokens?: number;
    showThinking?: boolean;
    bufferSize?: number;
  } = {},
  onStreamEnd?: StreamEndCallback,
) => {
  const {
    thinkingBudget = 8000,
    temperature = 1,
    maxTokens = 10000,
    showThinking = true,
    bufferSize = 100,
  } = options;

  try {
    if (!persona) {
      throw new Error('人设信息不能为空');
    }

    // 发送开始消息
    await sendClaudeMessageToClient(connectionId, {
      taskId,
      type: 'start',
      message: '正在启动Claude思考过程，开始生成内容...',
    });

    // 生成提示词
    const prompt = await createFinalContentPrompt(candidateContent, persona, selectedPhotos || []);

    // 创建消息
    const messages: MessagesAPIMessage[] = [createMessagesAPITextMessage(prompt)];

    // 创建思考配置
    const thinkingConfig = createThinkingConfig(thinkingBudget);

    // 创建API配置
    const config = createMessagesAPIConfig(maxTokens, {
      temperature,
      thinking: thinkingConfig,
      anthropic_beta: ['interleaved-thinking-2025-05-14'], // 添加思考过程的beta header
      system: `你是一个专业的内容创作专家，擅长根据人设特点和候选内容生成高质量的社交媒体内容。
请仔细分析提供的人设信息、候选内容和照片描述，生成符合人设特点的最终内容。
请确保输出格式为有效的JSON数组，每个元素包含finalContent、title、tags、style、reasoning、photos等字段。`,
    });

    // 创建增强的流处理器，支持内容收集和结束回调
    const streamHandler = createEnhancedStreamHandler(
      connectionId,
      taskId,
      {
        sendThinking: showThinking,
        sendProgress: true,
        bufferSize,
      },
      onStreamEnd,
    );

    // 开始流式传输
    const modelId = 'us.anthropic.claude-opus-4-20250514-v1:0';
    await invokeClaudeWithThinkingStream(
      modelId, // 使用Claude Sonnet 4
      messages,
      config,
      streamHandler,
    );

    console.log(`Claude思考过程内容生成完成 - 连接ID: ${connectionId}`);
  } catch (error) {
    console.error('Claude思考过程内容生成失败:', error);

    // 发送错误消息
    await sendClaudeMessageToClient(connectionId, {
      taskId,
      type: 'error',
      error: error instanceof Error ? error.message : '生成内容时发生未知错误',
    });

    // 发送备用内容
    const fallbackContent = `作为${persona.name}，我想和大家分享一些个人感悟。

${persona.personality}的我，总是能从生活中发现不一样的美好。${persona.introduction}

今天想聊聊${persona.topics.split(',')[0]?.trim() || '生活'}这个话题。每个人都有自己独特的视角和体验，而我希望通过我的分享，能够给大家带来一些启发和思考。

生活中的每一个瞬间都值得被记录和珍惜，让我们一起在平凡中发现不平凡，在日常中创造美好。`;

    const fallbackResult = [
      {
        finalContent: fallbackContent,
        title: '生活感悟分享',
        tags: ['生活', '分享', '感悟'],
        style: '个人风格',
        reasoning: `由于AI服务异常，使用了基于人设的备用内容: ${error instanceof Error ? error.message : '未知错误'}`,
        photos: selectedPhotos.map((photo) => photo.id),
        createdAt: new Date().toISOString(),
        status: 'INIT',
        id: uuid4(),
      },
    ];

    // 通过WebSocket发送备用内容
    await sendClaudeMessageToClient(connectionId, {
      taskId,
      type: 'text',
      content: JSON.stringify(fallbackResult, null, 2),
    });

    await sendClaudeMessageToClient(connectionId, {
      taskId,
      type: 'complete',
    });
  }
};

/**
 * 使用示例：
 *
 * const onStreamEnd = async (result) => {
 *   console.log('思考过程:', result.thinkingContent);
 *   console.log('正式内容:', result.finalContent);
 *   console.log('任务ID:', result.taskId);
 *   console.log('连接ID:', result.connectionId);
 *
 *   // 可以在这里进行后续处理，比如：
 *   // 1. 保存到数据库
 *   // 2. 发送通知
 *   // 3. 触发其他业务逻辑
 * };
 *
 * await generateContentWithThinking(
 *   connectionId,
 *   taskId,
 *   candidateContent,
 *   persona,
 *   selectedPhotos,
 *   options,
 *   onStreamEnd
 * );
 */
