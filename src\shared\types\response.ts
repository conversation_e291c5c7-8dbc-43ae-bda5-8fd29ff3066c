/**
 * 统一API响应格式定义
 */

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: number;
    requestId: string;
    version: string;
  };
}

/**
 * 分页响应格式
 */
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 错误码枚举
 */
export enum ErrorCode {
  // 认证错误 1xxx
  UNAUTHORIZED = '1001',
  TOKEN_EXPIRED = '1002',
  INVALID_CREDENTIALS = '1003',
  ACCESS_DENIED = '1004',

  // 验证错误 2xxx
  VALIDATION_ERROR = '2001',
  MISSING_REQUIRED_FIELD = '2002',
  INVALID_FORMAT = '2003',

  // 业务错误 3xxx
  PDF_NOT_FOUND = '3001',
  PDF_PROCESSING_FAILED = '3002',
  PDF_UPLOAD_FAILED = '3003',
  WORKSPACE_NOT_FOUND = '3004',
  USER_NOT_FOUND = '3005',
  EMAIL_ALREADY_EXISTS = '3006',
  PHONE_ALREADY_EXISTS = '3008',
  SUBSCRIPTION_NOT_FOUND = '3007',
  PERSONA_CREATE_FAILED = '3009',
  PERSONA_LIST_FAILED = '3010',
  PERSONA_UPDATE_FAILED = '3011',
  PERSONA_NOT_FOUND = '3012',
  PHOTO_UPLOAD_FAILED = '3013',
  PHOTO_LIST_FAILED = '3014',
  PHOTO_STATISTICS_FAILED = '3015',
  FILE_IMPORT_FAILED = '3016',
  FILE_LIST_FAILED = '3017',
  SOURCE_CREATE_FAILED = '3018',
  SOURCE_LIST_FAILED = '3019',
  SOURCE_UPDATE_FAILED = '3020',
  SOURCE_DELETE_FAILED = '3021',
  SOURCE_NOT_FOUND = '3022',

  // 外部服务错误 4xxx
  AZURE_AI_ERROR = '4001',
  BEDROCK_ERROR = '4002',
  MILVUS_ERROR = '4003',
  S3_ERROR = '4004',
  SQS_ERROR = '4005',
  PADDLE_ERROR = '4006',

  // 系统错误 5xxx
  INTERNAL_ERROR = '5001',
  DATABASE_ERROR = '5002',
  EXTERNAL_SERVICE_ERROR = '5003',
  TIMEOUT_ERROR = '5004',
  RATE_LIMIT_EXCEEDED = '5005',
}

/**
 * 错误消息映射
 */
export const ErrorMessages: Record<ErrorCode, string> = {
  // 认证错误
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.TOKEN_EXPIRED]: 'Token已过期',
  [ErrorCode.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ErrorCode.ACCESS_DENIED]: '访问被拒绝',

  // 验证错误
  [ErrorCode.VALIDATION_ERROR]: '输入验证失败',
  [ErrorCode.MISSING_REQUIRED_FIELD]: '缺少必填字段',
  [ErrorCode.INVALID_FORMAT]: '格式不正确',

  // 业务错误
  [ErrorCode.PDF_NOT_FOUND]: 'PDF文件未找到',
  [ErrorCode.PDF_PROCESSING_FAILED]: 'PDF处理失败',
  [ErrorCode.PDF_UPLOAD_FAILED]: 'PDF上传失败',
  [ErrorCode.WORKSPACE_NOT_FOUND]: '工作空间未找到',
  [ErrorCode.USER_NOT_FOUND]: '用户未找到',
  [ErrorCode.EMAIL_ALREADY_EXISTS]: '邮箱已存在',
  [ErrorCode.PHONE_ALREADY_EXISTS]: '手机号已存在',
  [ErrorCode.SUBSCRIPTION_NOT_FOUND]: '订阅未找到',
  [ErrorCode.PERSONA_CREATE_FAILED]: '人设创建失败',
  [ErrorCode.PERSONA_LIST_FAILED]: '获取人设列表失败',
  [ErrorCode.PERSONA_UPDATE_FAILED]: '人设更新失败',
  [ErrorCode.PERSONA_NOT_FOUND]: '人设不存在',
  [ErrorCode.PHOTO_UPLOAD_FAILED]: '照片上传失败',
  [ErrorCode.PHOTO_LIST_FAILED]: '获取照片列表失败',
  [ErrorCode.PHOTO_STATISTICS_FAILED]: '获取照片统计信息失败',
  [ErrorCode.FILE_IMPORT_FAILED]: '文件导入失败',
  [ErrorCode.FILE_LIST_FAILED]: '获取文件列表失败',
  [ErrorCode.SOURCE_CREATE_FAILED]: '信息源创建失败',
  [ErrorCode.SOURCE_LIST_FAILED]: '获取信息源列表失败',
  [ErrorCode.SOURCE_UPDATE_FAILED]: '信息源更新失败',
  [ErrorCode.SOURCE_DELETE_FAILED]: '信息源删除失败',
  [ErrorCode.SOURCE_NOT_FOUND]: '信息源不存在',

  // 外部服务错误
  [ErrorCode.AZURE_AI_ERROR]: 'Azure AI服务错误',
  [ErrorCode.BEDROCK_ERROR]: 'AWS Bedrock服务错误',
  [ErrorCode.MILVUS_ERROR]: 'Milvus向量数据库错误',
  [ErrorCode.S3_ERROR]: 'S3存储服务错误',
  [ErrorCode.SQS_ERROR]: 'SQS队列服务错误',
  [ErrorCode.PADDLE_ERROR]: 'Paddle支付服务错误',

  // 系统错误
  [ErrorCode.INTERNAL_ERROR]: '内部服务器错误',
  [ErrorCode.DATABASE_ERROR]: '数据库错误',
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: '外部服务错误',
  [ErrorCode.TIMEOUT_ERROR]: '请求超时',
  [ErrorCode.RATE_LIMIT_EXCEEDED]: '请求频率超限',
};

/**
 * HTTP状态码映射
 */
export const HttpStatusMap: Record<string, number> = {
  // 认证错误
  [ErrorCode.UNAUTHORIZED]: 401,
  [ErrorCode.TOKEN_EXPIRED]: 401,
  [ErrorCode.INVALID_CREDENTIALS]: 401,
  [ErrorCode.ACCESS_DENIED]: 403,

  // 验证错误
  [ErrorCode.VALIDATION_ERROR]: 400,
  [ErrorCode.MISSING_REQUIRED_FIELD]: 400,
  [ErrorCode.INVALID_FORMAT]: 400,

  // 业务错误
  [ErrorCode.PDF_NOT_FOUND]: 404,
  [ErrorCode.WORKSPACE_NOT_FOUND]: 404,
  [ErrorCode.USER_NOT_FOUND]: 404,
  [ErrorCode.SUBSCRIPTION_NOT_FOUND]: 404,
  [ErrorCode.EMAIL_ALREADY_EXISTS]: 409,
  [ErrorCode.PHONE_ALREADY_EXISTS]: 409,
  [ErrorCode.PDF_PROCESSING_FAILED]: 422,
  [ErrorCode.PDF_UPLOAD_FAILED]: 422,
  [ErrorCode.PERSONA_CREATE_FAILED]: 422,
  [ErrorCode.PERSONA_LIST_FAILED]: 422,
  [ErrorCode.PERSONA_UPDATE_FAILED]: 422,
  [ErrorCode.PERSONA_NOT_FOUND]: 404,
  [ErrorCode.PHOTO_UPLOAD_FAILED]: 422,
  [ErrorCode.PHOTO_LIST_FAILED]: 422,
  [ErrorCode.PHOTO_STATISTICS_FAILED]: 422,
  [ErrorCode.FILE_IMPORT_FAILED]: 422,
  [ErrorCode.FILE_LIST_FAILED]: 422,
  [ErrorCode.SOURCE_CREATE_FAILED]: 422,
  [ErrorCode.SOURCE_LIST_FAILED]: 422,
  [ErrorCode.SOURCE_UPDATE_FAILED]: 422,
  [ErrorCode.SOURCE_DELETE_FAILED]: 422,
  [ErrorCode.SOURCE_NOT_FOUND]: 404,

  // 外部服务错误
  [ErrorCode.AZURE_AI_ERROR]: 502,
  [ErrorCode.BEDROCK_ERROR]: 502,
  [ErrorCode.MILVUS_ERROR]: 502,
  [ErrorCode.S3_ERROR]: 502,
  [ErrorCode.SQS_ERROR]: 502,
  [ErrorCode.PADDLE_ERROR]: 502,

  // 系统错误
  [ErrorCode.INTERNAL_ERROR]: 500,
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 502,
  [ErrorCode.TIMEOUT_ERROR]: 504,
  [ErrorCode.RATE_LIMIT_EXCEEDED]: 429,
};
