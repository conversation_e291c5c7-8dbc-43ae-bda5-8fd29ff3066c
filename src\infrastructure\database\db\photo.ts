import { PutCommand, QueryCommand, UpdateCommand, GetCommand } from '@aws-sdk/lib-dynamodb';

import { IPhoto } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_PHOTO';

/**
 * 根据照片ID获取照片信息
 * @param photoId 照片ID
 * @returns 照片信息
 */
export async function getPhotoById(photoId: string): Promise<IPhoto | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { id: photoId },
    });
    const result = await dbClient.send(command);
    return (result.Item as IPhoto) || null;
  } catch (error) {
    console.error('获取照片信息失败', error);
    throw error;
  }
}

/**
 * 创建照片
 * @param data 照片数据
 */
export async function createPhoto(data: IPhoto) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建照片失败', error);
    throw error;
  }
}

/**
 * 检查照片是否已存在（根据用户ID、人设ID和文件名）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param fileName 文件名
 * @returns 是否存在
 */
export async function checkPhotoExists(
  userId: string,
  personaId: string,
  fileName: string,
): Promise<boolean> {
  try {
    // 使用简单的 GSI：userId-index，然后过滤
    const command = new QueryCommand({
      TableName: TABLE_NAME,
      IndexName: 'userId-index', // 只需要简单的 userId GSI
      KeyConditionExpression: 'userId = :userId',
      FilterExpression: 'personaId = :personaId AND fileName = :fileName',
      ExpressionAttributeValues: {
        ':userId': userId,
        ':personaId': personaId,
        ':fileName': fileName,
      },
      Limit: 1,
    });

    const result = await dbClient.send(command);
    return (result.Count || 0) > 0;
  } catch (error) {
    console.error('检查照片存在性失败', error);
    throw error;
  }
}

/**
 * 批量创建照片（跳过重名文件）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param photos 照片数据数组
 * @returns 创建结果统计
 */
export async function batchCreatePhotos(
  userId: string,
  personaId: string,
  photos: Array<{ fileName: string; s3Key: string }>,
): Promise<{
  created: number;
  skipped: number;
  errors: number;
  details: Array<{
    fileName: string;
    status: 'created' | 'skipped' | 'error';
    message?: string;
    photoId?: string;
    s3Key?: string;
  }>;
}> {
  // 处理单个照片的函数
  const processPhoto = async (photo: { fileName: string; s3Key: string }) => {
    try {
      // 检查文件是否已存在
      const exists = await checkPhotoExists(userId, personaId, photo.fileName);

      if (exists) {
        return {
          fileName: photo.fileName,
          status: 'skipped' as const,
          message: '文件已存在',
        };
      }

      // 创建照片记录
      const photoData: IPhoto = {
        id: `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        userId,
        personaId,
        fileName: photo.fileName,
        s3Key: photo.s3Key,
        isUse: false,
        summary: '正在生成摘要...', // AI 摘要将异步生成
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await createPhoto(photoData);
      return {
        fileName: photo.fileName,
        status: 'created' as const,
        photoId: photoData.id,
        s3Key: photo.s3Key,
      };
    } catch (error) {
      console.error(`创建照片失败: ${photo.fileName}`, error);
      return {
        fileName: photo.fileName,
        status: 'error' as const,
        message: error instanceof Error ? error.message : '未知错误',
      };
    }
  };

  // 并行处理所有照片
  const results = await Promise.all(photos.map(processPhoto));

  // 统计结果
  const summary = results.reduce(
    (acc, result) => {
      if (result.status === 'created') {
        return { ...acc, created: acc.created + 1 };
      }
      if (result.status === 'skipped') {
        return { ...acc, skipped: acc.skipped + 1 };
      }
      return { ...acc, errors: acc.errors + 1 };
    },
    { created: 0, skipped: 0, errors: 0 },
  );

  return {
    ...summary,
    details: results,
  };
}

/**
 * 更新照片的 summary
 * @param photoId 照片ID
 * @param summary AI生成的摘要
 */
export async function updatePhotoSummary(photoId: string, summary: string): Promise<void> {
  try {
    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { id: photoId },
      UpdateExpression: 'SET summary = :summary, updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':summary': summary,
        ':updatedAt': new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('更新照片摘要失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID获取照片列表（返回全部数据）
 * @param userId 用户ID
 * @param personaId 人设ID（可选）
 * @returns 照片列表和分页信息
 */
/* eslint-disable no-await-in-loop */
export async function getPhotosByUserAndPersona(
  userId: string,
  personaId?: string,
): Promise<{
  photos: IPhoto[];
  lastEvaluatedKey?: any;
  count: number;
}> {
  try {
    const allPhotos: IPhoto[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      let command: QueryCommand;

      if (personaId) {
        // 查询特定人设的照片
        command = new QueryCommand({
          TableName: TABLE_NAME,
          IndexName: 'userId-index',
          KeyConditionExpression: 'userId = :userId',
          FilterExpression: 'personaId = :personaId',
          ExpressionAttributeValues: {
            ':userId': userId,
            ':personaId': personaId,
          },
          ExclusiveStartKey: currentLastEvaluatedKey,
          ScanIndexForward: false, // 按创建时间倒序
        });
      } else {
        // 查询用户的所有照片
        command = new QueryCommand({
          TableName: TABLE_NAME,
          IndexName: 'userId-index',
          KeyConditionExpression: 'userId = :userId',
          ExpressionAttributeValues: {
            ':userId': userId,
          },
          ExclusiveStartKey: currentLastEvaluatedKey,
          ScanIndexForward: false, // 按创建时间倒序
        });
      }

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const photos = (result.Items as IPhoto[]) || [];

      allPhotos.push(...photos);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return {
      photos: allPhotos,
      lastEvaluatedKey: undefined, // 返回全部数据时不需要分页键
      count: allPhotos.length,
    };
  } catch (error) {
    console.error('获取照片列表失败', error);
    throw error;
  }
}

/**
 * 获取照片统计信息（支持大数据量分页查询）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @returns 照片统计信息
 */
export async function getPhotoStatistics(
  userId: string,
  personaId: string,
): Promise<{
  total: number;
  used: number;
  unused: number;
  typeStatistics: Array<{ type: string; count: number }>;
}> {
  try {
    let allPhotos: any[] = [];
    let lastEvaluatedKey: any;
    let hasMore = true;

    // 分页查询所有照片数据
    // eslint-disable-next-line no-await-in-loop
    while (hasMore) {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-index',
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'personaId = :personaId',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
        },
        ProjectionExpression: 'isUse, summary', // 只获取需要的字段
        ExclusiveStartKey: lastEvaluatedKey,
        Limit: 1000, // 每次最多查询1000条
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);
      const photos = result.Items || [];

      // 累积所有照片数据
      allPhotos = allPhotos.concat(photos);

      // 检查是否还有更多数据
      lastEvaluatedKey = result.LastEvaluatedKey;
      hasMore = !!lastEvaluatedKey;

      // 添加日志以便监控大数据量查询
      if (allPhotos.length > 5000) {
        console.log(
          `Large dataset detected: ${allPhotos.length} photos for user ${userId}, persona ${personaId}`,
        );
      }
    }

    // 统计总数
    const total = allPhotos.length;

    // 统计使用状态
    const used = allPhotos.filter((photo: any) => photo.isUse === true).length;
    const unused = total - used;

    // 统计分类信息
    const typeMap = new Map<string, number>();

    allPhotos.forEach((photo: any) => {
      try {
        // 尝试解析 AI 生成的 JSON 格式摘要
        if (photo.summary && photo.summary.trim().startsWith('{')) {
          const parsed = JSON.parse(photo.summary);
          const type = parsed.type || '未分类';
          typeMap.set(type, (typeMap.get(type) || 0) + 1);
        } else {
          // 如果不是 JSON 格式，归类为"其他"
          typeMap.set('其他', (typeMap.get('其他') || 0) + 1);
        }
      } catch (error) {
        // JSON 解析失败，归类为"其他"
        typeMap.set('其他', (typeMap.get('其他') || 0) + 1);
      }
    });

    // 转换为数组格式并按数量排序
    const typeStatistics = Array.from(typeMap.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    console.log(
      `Photo statistics completed: ${total} photos, ${typeStatistics.length} types for user ${userId}, persona ${personaId}`,
    );

    return {
      total,
      used,
      unused,
      typeStatistics,
    };
  } catch (error) {
    console.error('获取照片统计信息失败', error);
    throw error;
  }
}

/**
 * 根据类型获取照片列表
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param type 类型 ('total' | 'used' | 'unused' | 其他AI分类类型)
 * @param limit 限制数量（默认50）
 * @param lastEvaluatedKey 分页键
 * @returns 照片列表和分页信息
 */
export async function getPhotosByType(
  userId: string,
  personaId: string,
  type: string,
  limit: number = 50,
  lastEvaluatedKey?: any,
): Promise<{
  photos: IPhoto[];
  lastEvaluatedKey?: any;
  count: number;
  totalCount: number;
}> {
  try {
    // 如果是 total 类型，直接返回所有照片
    if (type === 'total') {
      const result = await getPhotosByUserAndPersona(userId, personaId);

      // 实现分页
      const startIndex = lastEvaluatedKey ? parseInt(lastEvaluatedKey.index || '0', 10) : 0;
      const endIndex = startIndex + limit;
      const paginatedPhotos = result.photos.slice(startIndex, endIndex);

      // 生成下一页的键
      const nextLastEvaluatedKey =
        endIndex < result.photos.length ? { index: endIndex.toString() } : undefined;

      return {
        photos: paginatedPhotos,
        lastEvaluatedKey: nextLastEvaluatedKey,
        count: paginatedPhotos.length,
        totalCount: result.count,
      };
    }

    // 对于其他类型，需要获取所有数据然后过滤
    let allPhotos: IPhoto[] = [];
    let queryLastEvaluatedKey: any;
    let hasMore = true;

    // 分页查询所有照片数据
    // eslint-disable-next-line no-await-in-loop
    while (hasMore) {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-index',
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'personaId = :personaId',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
        },
        ExclusiveStartKey: queryLastEvaluatedKey,
        Limit: 1000, // 每次最多查询1000条
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);

      if (result.Items && result.Items.length > 0) {
        allPhotos = allPhotos.concat(result.Items as IPhoto[]);
      }

      queryLastEvaluatedKey = result.LastEvaluatedKey;
      hasMore = !!queryLastEvaluatedKey;
    }

    // 根据类型过滤照片
    let filteredPhotos: IPhoto[] = [];

    if (type === 'used') {
      filteredPhotos = allPhotos.filter((photo) => photo.isUse === true);
    } else if (type === 'unused') {
      filteredPhotos = allPhotos.filter((photo) => photo.isUse === false);
    } else {
      // 根据AI摘要中的type字段过滤
      filteredPhotos = allPhotos.filter((photo) => {
        try {
          if (photo.summary && photo.summary.trim().startsWith('{')) {
            const parsed = JSON.parse(photo.summary);
            return parsed.type === type;
          }
          return false;
        } catch (error) {
          return false;
        }
      });
    }

    // 按创建时间倒序排序
    filteredPhotos.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    // 实现分页
    const startIndex = lastEvaluatedKey ? parseInt(lastEvaluatedKey.index || '0', 10) : 0;
    const endIndex = startIndex + limit;
    const paginatedPhotos = filteredPhotos.slice(startIndex, endIndex);

    // 生成下一页的键
    const nextLastEvaluatedKey =
      endIndex < filteredPhotos.length ? { index: endIndex.toString() } : undefined;

    return {
      photos: paginatedPhotos,
      lastEvaluatedKey: nextLastEvaluatedKey,
      count: paginatedPhotos.length,
      totalCount: filteredPhotos.length,
    };
  } catch (error) {
    console.error('根据类型获取照片列表失败', error);
    throw error;
  }
}

// updatePhotoUsage
export async function updatePhotoUsage(photoId: string, isUse: boolean): Promise<void> {
  try {
    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { id: photoId },
      UpdateExpression: 'SET isUse = :isUse, updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':isUse': isUse,
        ':updatedAt': new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('更新照片使用状态失败', error);
    throw error;
  }
}
